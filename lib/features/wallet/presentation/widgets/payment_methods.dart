import 'package:escooter/core/configs/theme/app_colors.dart';
import 'package:escooter/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/configs/constants/app_localization_constants.dart';
import '../../../payment/presentation/providers/payment_method_provider.dart';
import '../../../payment/presentation/widgets/payment_method_card.dart';

class PaymentMethods extends ConsumerWidget {
  final bool isDarkMode;

  const PaymentMethods({required this.isDarkMode, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final localizations = ref.watch(appLocalizationsProvider);
    final theme = Theme.of(context);
    final paymentMethodsAsync = ref.watch(paymentMethodsProvider);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                localizations
                    .translate(AppLocalizationConstants.paymentMethods),
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black87,
                ),
              ),
              TextButton(
                onPressed: () {
                  context.push('/payment-methods');
                },
                child: Text(
                  localizations.translate(AppLocalizationConstants.seeAll),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: AppColors.primaryTeal,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          paymentMethodsAsync.when(
            data: (paymentMethods) {
              final enabledMethods = paymentMethods.where((m) => m.isEnabled).take(3).toList();

              if (enabledMethods.isEmpty) {
                return _buildEmptyState(context, localizations, theme);
              }

              return Column(
                children: [
                  ...enabledMethods.map((method) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: PaymentMethodCard(
                      paymentMethod: method,
                      isDarkMode: isDarkMode,
                      onTap: () => context.push('/payment-methods'),
                    ),
                  )),
                ],
              );
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => _buildErrorState(context, theme),
          ),
          const SizedBox(height: 16),
          OutlinedButton.icon(
            onPressed: () {
              context.push('/payment-methods');
            },
            style: OutlinedButton.styleFrom(
              foregroundColor:
                  isDarkMode ? Colors.white : theme.colorScheme.primary,
              side: BorderSide(
                color: isDarkMode ? Colors.white70 : theme.colorScheme.primary,
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 12,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            icon: Icon(
              Icons.add,
              color: isDarkMode ? Colors.white : theme.colorScheme.primary,
            ),
            label: Text(
              localizations
                  .translate(AppLocalizationConstants.addNewPaymentMethod),
              style: TextStyle(
                color: isDarkMode ? Colors.white : theme.colorScheme.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, dynamic localizations, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Icon(
            Icons.payment,
            size: 48,
            color: theme.colorScheme.primary.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No payment methods added',
            style: theme.textTheme.titleMedium?.copyWith(
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add a payment method to get started',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isDarkMode ? Colors.white70 : Colors.black54,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: theme.colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to load payment methods',
            style: theme.textTheme.titleMedium?.copyWith(
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
        ],
      ),
    );
  }
}
