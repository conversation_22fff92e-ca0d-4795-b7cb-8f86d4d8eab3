import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:injectable/injectable.dart';
import '../../../../core/configs/services/api/base_api.dart';
import '../../../../core/configs/services/storage/storage_service.dart';
import '../../../../core/error/api_exceptions.dart';
import '../../../../utils/logger.dart';
import '../models/payment_method_model.dart';

@injectable
class PaymentMethodService {
  final http.Client _client;
  final StorageService _storageService;

  PaymentMethodService(@Named('httpClient') this._client, this._storageService);

  Future<List<PaymentMethodModel>> getPaymentMethods() async {
    try {
      final user = _storageService.getUser();
      if (user == null) throw ApiException('User not authenticated');

      AppLogger.log('Fetching payment methods for user: ${user.id}');

      final response = await _client.get(
        Uri.parse('${BaseApi.basePaymentUrl}/v1/payment-methods'),
        headers: {
          'Authorization': 'Bearer ${user.token}',
          'Content-Type': 'application/json',
        },
      );

      AppLogger.log('Payment methods response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final List<dynamic> paymentMethodsJson = data['paymentMethods'] ?? [];
        
        return paymentMethodsJson
            .map((json) => PaymentMethodModel.fromJson(json))
            .toList();
      }

      throw ApiException.fromResponse(response);
    } catch (e) {
      AppLogger.error('Failed to fetch payment methods', error: e);
      if (e is ApiException) rethrow;
      throw ApiException('Failed to fetch payment methods: $e');
    }
  }

  Future<PaymentMethodModel> addPaymentMethod(PaymentMethodModel paymentMethod) async {
    try {
      final user = _storageService.getUser();
      if (user == null) throw ApiException('User not authenticated');

      AppLogger.log('Adding payment method: ${paymentMethod.type.name}');

      final response = await _client.post(
        Uri.parse('${BaseApi.basePaymentUrl}/v1/payment-methods'),
        headers: {
          'Authorization': 'Bearer ${user.token}',
          'Content-Type': 'application/json',
        },
        body: jsonEncode(paymentMethod.toJson()),
      );

      AppLogger.log('Add payment method response: ${response.statusCode}');

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        return PaymentMethodModel.fromJson(data['paymentMethod']);
      }

      throw ApiException.fromResponse(response);
    } catch (e) {
      AppLogger.error('Failed to add payment method', error: e);
      if (e is ApiException) rethrow;
      throw ApiException('Failed to add payment method: $e');
    }
  }

  Future<bool> removePaymentMethod(String paymentMethodId) async {
    try {
      final user = _storageService.getUser();
      if (user == null) throw ApiException('User not authenticated');

      AppLogger.log('Removing payment method: $paymentMethodId');

      final response = await _client.delete(
        Uri.parse('${BaseApi.basePaymentUrl}/v1/payment-methods/$paymentMethodId'),
        headers: {
          'Authorization': 'Bearer ${user.token}',
          'Content-Type': 'application/json',
        },
      );

      AppLogger.log('Remove payment method response: ${response.statusCode}');

      return response.statusCode == 200;
    } catch (e) {
      AppLogger.error('Failed to remove payment method', error: e);
      if (e is ApiException) rethrow;
      throw ApiException('Failed to remove payment method: $e');
    }
  }

  Future<bool> processPayment({
    required String paymentMethodId,
    required double amount,
    required String currency,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final user = _storageService.getUser();
      if (user == null) throw ApiException('User not authenticated');

      AppLogger.log('Processing payment: $amount $currency with method: $paymentMethodId');

      final response = await _client.post(
        Uri.parse('${BaseApi.basePaymentUrl}/v1/payments/process'),
        headers: {
          'Authorization': 'Bearer ${user.token}',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'paymentMethodId': paymentMethodId,
          'amount': amount,
          'currency': currency,
          'metadata': metadata,
        }),
      );

      AppLogger.log('Process payment response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['success'] == true;
      }

      throw ApiException.fromResponse(response);
    } catch (e) {
      AppLogger.error('Failed to process payment', error: e);
      if (e is ApiException) rethrow;
      throw ApiException('Failed to process payment: $e');
    }
  }
}
