import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/error/failures.dart';
import '../../../../utils/logger.dart';
import '../../domain/entities/payment_method.dart';
import '../../domain/repositories/payment_method_repository.dart';
import '../models/payment_method_model.dart';
import '../services/payment_method_service.dart';

@LazySingleton(as: PaymentMethodRepository)
class PaymentMethodRepositoryImpl implements PaymentMethodRepository {
  final PaymentMethodService _service;

  PaymentMethodRepositoryImpl(this._service);

  @override
  Future<Either<Failure, List<PaymentMethod>>> getPaymentMethods() async {
    try {
      AppLogger.log('Repository: Fetching payment methods');
      
      // For now, return mock data since we don't have backend
      // In production, uncomment the line below:
      // final paymentMethods = await _service.getPaymentMethods();
      
      final paymentMethods = PaymentMethodModel.getMockPaymentMethods();
      
      AppLogger.log('Repository: Found ${paymentMethods.length} payment methods');
      return Right(paymentMethods);
    } catch (e) {
      AppLogger.error('Repository: Failed to fetch payment methods', error: e);
      return Left(ServerFailure('Failed to fetch payment methods: $e'));
    }
  }

  @override
  Future<Either<Failure, PaymentMethod>> addPaymentMethod(PaymentMethod paymentMethod) async {
    try {
      AppLogger.log('Repository: Adding payment method: ${paymentMethod.type.name}');
      
      // For now, return the same payment method since we don't have backend
      // In production, uncomment the lines below:
      // final model = PaymentMethodModel.fromEntity(paymentMethod);
      // final result = await _service.addPaymentMethod(model);
      
      AppLogger.log('Repository: Payment method added successfully');
      return Right(paymentMethod);
    } catch (e) {
      AppLogger.error('Repository: Failed to add payment method', error: e);
      return Left(ServerFailure('Failed to add payment method: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> removePaymentMethod(String paymentMethodId) async {
    try {
      AppLogger.log('Repository: Removing payment method: $paymentMethodId');
      
      // For now, return true since we don't have backend
      // In production, uncomment the line below:
      // final result = await _service.removePaymentMethod(paymentMethodId);
      
      AppLogger.log('Repository: Payment method removed successfully');
      return const Right(true);
    } catch (e) {
      AppLogger.error('Repository: Failed to remove payment method', error: e);
      return Left(ServerFailure('Failed to remove payment method: $e'));
    }
  }

  @override
  Future<Either<Failure, PaymentMethod>> setDefaultPaymentMethod(String paymentMethodId) async {
    try {
      AppLogger.log('Repository: Setting default payment method: $paymentMethodId');
      
      // For now, return a mock payment method since we don't have backend
      final mockMethods = PaymentMethodModel.getMockPaymentMethods();
      final method = mockMethods.firstWhere(
        (m) => m.id == paymentMethodId,
        orElse: () => mockMethods.first,
      );
      
      final updatedMethod = method.copyWith(isDefault: true);
      
      AppLogger.log('Repository: Default payment method set successfully');
      return Right(updatedMethod);
    } catch (e) {
      AppLogger.error('Repository: Failed to set default payment method', error: e);
      return Left(ServerFailure('Failed to set default payment method: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> processPayment({
    required String paymentMethodId,
    required double amount,
    required String currency,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      AppLogger.log('Repository: Processing payment: $amount $currency');
      
      // For now, simulate payment processing
      // In production, uncomment the lines below:
      // final result = await _service.processPayment(
      //   paymentMethodId: paymentMethodId,
      //   amount: amount,
      //   currency: currency,
      //   metadata: metadata,
      // );
      
      // Simulate processing delay
      await Future.delayed(const Duration(seconds: 2));
      
      AppLogger.log('Repository: Payment processed successfully');
      return const Right(true);
    } catch (e) {
      AppLogger.error('Repository: Failed to process payment', error: e);
      return Left(ServerFailure('Failed to process payment: $e'));
    }
  }
}
