import '../../domain/entities/payment_method.dart';

class PaymentMethodModel extends PaymentMethod {
  const PaymentMethodModel({
    required super.id,
    required super.type,
    required super.displayName,
    super.lastFourDigits,
    super.cardBrand,
    super.isDefault,
    super.isEnabled,
    required super.createdAt,
    super.expiryDate,
    super.metadata,
  });

  factory PaymentMethodModel.fromJson(Map<String, dynamic> json) {
    return PaymentMethodModel(
      id: json['id'] as String,
      type: PaymentMethodType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => PaymentMethodType.wallet,
      ),
      displayName: json['displayName'] as String,
      lastFourDigits: json['lastFourDigits'] as String?,
      cardBrand: json['cardBrand'] as String?,
      isDefault: json['isDefault'] as bool? ?? false,
      isEnabled: json['isEnabled'] as bool? ?? true,
      createdAt: DateTime.parse(json['createdAt'] as String),
      expiryDate: json['expiryDate'] != null 
          ? DateTime.parse(json['expiryDate'] as String)
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  factory PaymentMethodModel.fromEntity(PaymentMethod entity) {
    return PaymentMethodModel(
      id: entity.id,
      type: entity.type,
      displayName: entity.displayName,
      lastFourDigits: entity.lastFourDigits,
      cardBrand: entity.cardBrand,
      isDefault: entity.isDefault,
      isEnabled: entity.isEnabled,
      createdAt: entity.createdAt,
      expiryDate: entity.expiryDate,
      metadata: entity.metadata,
    );
  }

  // Create mock payment methods for testing
  static List<PaymentMethodModel> getMockPaymentMethods() {
    return [
      PaymentMethodModel(
        id: 'wallet_001',
        type: PaymentMethodType.wallet,
        displayName: 'Wallet Balance',
        isDefault: true,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        metadata: {'balance': 25.50},
      ),
      PaymentMethodModel(
        id: 'google_pay_001',
        type: PaymentMethodType.googlePay,
        displayName: 'Google Pay',
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
      ),
      PaymentMethodModel(
        id: 'apple_pay_001',
        type: PaymentMethodType.applePay,
        displayName: 'Apple Pay',
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
      ),
      PaymentMethodModel(
        id: 'mada_001',
        type: PaymentMethodType.mada,
        displayName: 'Mada Card',
        lastFourDigits: '1234',
        cardBrand: 'Mada',
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        expiryDate: DateTime(2027, 12, 31),
      ),
      PaymentMethodModel(
        id: 'visa_001',
        type: PaymentMethodType.creditCard,
        displayName: 'Visa Credit Card',
        lastFourDigits: '5678',
        cardBrand: 'Visa',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        expiryDate: DateTime(2026, 8, 31),
      ),
    ];
  }
}
