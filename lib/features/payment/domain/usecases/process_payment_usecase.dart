import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/payment_method_repository.dart';

class ProcessPaymentParams {
  final String paymentMethodId;
  final double amount;
  final String currency;
  final Map<String, dynamic>? metadata;

  ProcessPaymentParams({
    required this.paymentMethodId,
    required this.amount,
    this.currency = 'SAR',
    this.metadata,
  });
}

@injectable
class ProcessPaymentUseCase implements UseCase<bool, ProcessPaymentParams> {
  final PaymentMethodRepository repository;

  ProcessPaymentUseCase(this.repository);

  @override
  Future<Either<Failure, bool>> call({required ProcessPaymentParams params}) async {
    return await repository.processPayment(
      paymentMethodId: params.paymentMethodId,
      amount: params.amount,
      currency: params.currency,
      metadata: params.metadata,
    );
  }
}
