import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/payment_method.dart';

abstract class PaymentMethodRepository {
  Future<Either<Failure, List<PaymentMethod>>> getPaymentMethods();
  Future<Either<Failure, PaymentMethod>> addPaymentMethod(PaymentMethod paymentMethod);
  Future<Either<Failure, bool>> removePaymentMethod(String paymentMethodId);
  Future<Either<Failure, PaymentMethod>> setDefaultPaymentMethod(String paymentMethodId);
  Future<Either<Failure, bool>> processPayment({
    required String paymentMethodId,
    required double amount,
    required String currency,
    Map<String, dynamic>? metadata,
  });
}
