enum PaymentMethodType {
  wallet,
  googlePay,
  applePay,
  mada,
  creditCard,
  debitCard,
}

class PaymentMethod {
  final String id;
  final PaymentMethodType type;
  final String displayName;
  final String? lastFourDigits;
  final String? cardBrand;
  final bool isDefault;
  final bool isEnabled;
  final DateTime createdAt;
  final DateTime? expiryDate;
  final Map<String, dynamic>? metadata;

  const PaymentMethod({
    required this.id,
    required this.type,
    required this.displayName,
    this.lastFourDigits,
    this.cardBrand,
    this.isDefault = false,
    this.isEnabled = true,
    required this.createdAt,
    this.expiryDate,
    this.metadata,
  });

  factory PaymentMethod.fromJson(Map<String, dynamic> json) {
    return PaymentMethod(
      id: json['id'] as String,
      type: PaymentMethodType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => PaymentMethodType.wallet,
      ),
      displayName: json['displayName'] as String,
      lastFourDigits: json['lastFourDigits'] as String?,
      cardBrand: json['cardBrand'] as String?,
      isDefault: json['isDefault'] as bool? ?? false,
      isEnabled: json['isEnabled'] as bool? ?? true,
      createdAt: DateTime.parse(json['createdAt'] as String),
      expiryDate: json['expiryDate'] != null 
          ? DateTime.parse(json['expiryDate'] as String)
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'displayName': displayName,
      'lastFourDigits': lastFourDigits,
      'cardBrand': cardBrand,
      'isDefault': isDefault,
      'isEnabled': isEnabled,
      'createdAt': createdAt.toIso8601String(),
      'expiryDate': expiryDate?.toIso8601String(),
      'metadata': metadata,
    };
  }

  PaymentMethod copyWith({
    String? id,
    PaymentMethodType? type,
    String? displayName,
    String? lastFourDigits,
    String? cardBrand,
    bool? isDefault,
    bool? isEnabled,
    DateTime? createdAt,
    DateTime? expiryDate,
    Map<String, dynamic>? metadata,
  }) {
    return PaymentMethod(
      id: id ?? this.id,
      type: type ?? this.type,
      displayName: displayName ?? this.displayName,
      lastFourDigits: lastFourDigits ?? this.lastFourDigits,
      cardBrand: cardBrand ?? this.cardBrand,
      isDefault: isDefault ?? this.isDefault,
      isEnabled: isEnabled ?? this.isEnabled,
      createdAt: createdAt ?? this.createdAt,
      expiryDate: expiryDate ?? this.expiryDate,
      metadata: metadata ?? this.metadata,
    );
  }

  String get iconPath {
    switch (type) {
      case PaymentMethodType.wallet:
        return 'assets/icons/wallet.png';
      case PaymentMethodType.googlePay:
        return 'assets/icons/google_pay.png';
      case PaymentMethodType.applePay:
        return 'assets/icons/apple_pay.png';
      case PaymentMethodType.mada:
        return 'assets/icons/mada.png';
      case PaymentMethodType.creditCard:
      case PaymentMethodType.debitCard:
        return _getCardIcon();
    }
  }

  String _getCardIcon() {
    switch (cardBrand?.toLowerCase()) {
      case 'visa':
        return 'assets/icons/visa.png';
      case 'mastercard':
        return 'assets/icons/mastercard.png';
      case 'amex':
        return 'assets/icons/amex.png';
      default:
        return 'assets/icons/credit_card.png';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PaymentMethod && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'PaymentMethod(id: $id, type: $type, displayName: $displayName)';
  }
}
