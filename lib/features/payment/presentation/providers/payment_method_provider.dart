import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/di/service_locator/service_locator.dart';
import '../../../../utils/logger.dart';
import '../../domain/entities/payment_method.dart';
import '../../domain/usecases/get_payment_methods_usecase.dart';
import '../../domain/usecases/process_payment_usecase.dart';
import '../../../../core/usecases/usecase.dart';

// Provider for payment methods list
final paymentMethodsProvider = AsyncNotifierProvider<PaymentMethodsNotifier, List<PaymentMethod>>(() {
  return PaymentMethodsNotifier();
});

// Provider for selected payment method
final selectedPaymentMethodProvider = StateProvider<PaymentMethod?>((ref) => null);

// Provider for payment processing state
final paymentProcessingProvider = StateProvider<bool>((ref) => false);

class PaymentMethodsNotifier extends AsyncNotifier<List<PaymentMethod>> {
  @override
  Future<List<PaymentMethod>> build() async {
    return await _fetchPaymentMethods();
  }

  Future<List<PaymentMethod>> _fetchPaymentMethods() async {
    try {
      AppLogger.log('PaymentMethodsNotifier: Fetching payment methods');
      
      final useCase = getIt<GetPaymentMethodsUseCase>();
      final result = await useCase.call();
      
      return result.fold(
        (failure) {
          AppLogger.error('PaymentMethodsNotifier: Failed to fetch payment methods', error: failure.message);
          throw Exception(failure.message);
        },
        (paymentMethods) {
          AppLogger.log('PaymentMethodsNotifier: Fetched ${paymentMethods.length} payment methods');
          return paymentMethods;
        },
      );
    } catch (e) {
      AppLogger.error('PaymentMethodsNotifier: Error fetching payment methods', error: e);
      rethrow;
    }
  }

  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _fetchPaymentMethods());
  }

  Future<bool> addPaymentMethod(PaymentMethod paymentMethod) async {
    try {
      AppLogger.log('PaymentMethodsNotifier: Adding payment method: ${paymentMethod.type.name}');
      
      // For now, just add to the current list since we don't have backend
      final currentMethods = state.value ?? [];
      final updatedMethods = [...currentMethods, paymentMethod];
      
      state = AsyncValue.data(updatedMethods);
      
      AppLogger.log('PaymentMethodsNotifier: Payment method added successfully');
      return true;
    } catch (e) {
      AppLogger.error('PaymentMethodsNotifier: Failed to add payment method', error: e);
      return false;
    }
  }

  Future<bool> removePaymentMethod(String paymentMethodId) async {
    try {
      AppLogger.log('PaymentMethodsNotifier: Removing payment method: $paymentMethodId');
      
      final currentMethods = state.value ?? [];
      final updatedMethods = currentMethods.where((method) => method.id != paymentMethodId).toList();
      
      state = AsyncValue.data(updatedMethods);
      
      AppLogger.log('PaymentMethodsNotifier: Payment method removed successfully');
      return true;
    } catch (e) {
      AppLogger.error('PaymentMethodsNotifier: Failed to remove payment method', error: e);
      return false;
    }
  }

  Future<bool> setDefaultPaymentMethod(String paymentMethodId) async {
    try {
      AppLogger.log('PaymentMethodsNotifier: Setting default payment method: $paymentMethodId');
      
      final currentMethods = state.value ?? [];
      final updatedMethods = currentMethods.map((method) {
        return method.copyWith(isDefault: method.id == paymentMethodId);
      }).toList();
      
      state = AsyncValue.data(updatedMethods);
      
      AppLogger.log('PaymentMethodsNotifier: Default payment method set successfully');
      return true;
    } catch (e) {
      AppLogger.error('PaymentMethodsNotifier: Failed to set default payment method', error: e);
      return false;
    }
  }
}

// Provider for processing payments
final processPaymentProvider = Provider<Future<bool> Function(ProcessPaymentParams)>((ref) {
  return (ProcessPaymentParams params) async {
    try {
      AppLogger.log('Processing payment: ${params.amount} ${params.currency}');
      
      // Set processing state
      ref.read(paymentProcessingProvider.notifier).state = true;
      
      final useCase = getIt<ProcessPaymentUseCase>();
      final result = await useCase.call(params: params);
      
      return result.fold(
        (failure) {
          AppLogger.error('Payment processing failed', error: failure.message);
          throw Exception(failure.message);
        },
        (success) {
          AppLogger.log('Payment processed successfully');
          return success;
        },
      );
    } catch (e) {
      AppLogger.error('Error processing payment', error: e);
      rethrow;
    } finally {
      // Reset processing state
      ref.read(paymentProcessingProvider.notifier).state = false;
    }
  };
});

// Helper provider to get default payment method
final defaultPaymentMethodProvider = Provider<PaymentMethod?>((ref) {
  final paymentMethods = ref.watch(paymentMethodsProvider);
  
  return paymentMethods.when(
    data: (methods) => methods.where((method) => method.isDefault).firstOrNull,
    loading: () => null,
    error: (_, __) => null,
  );
});

// Helper provider to get enabled payment methods
final enabledPaymentMethodsProvider = Provider<List<PaymentMethod>>((ref) {
  final paymentMethods = ref.watch(paymentMethodsProvider);
  
  return paymentMethods.when(
    data: (methods) => methods.where((method) => method.isEnabled).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});
