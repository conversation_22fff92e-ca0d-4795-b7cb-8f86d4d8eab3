import 'package:flutter/material.dart';
import '../../domain/entities/payment_method.dart';

class PaymentMethodCard extends StatelessWidget {
  final PaymentMethod paymentMethod;
  final bool isDarkMode;
  final VoidCallback? onTap;
  final VoidCallback? onSetDefault;
  final VoidCallback? onRemove;

  const PaymentMethodCard({
    super.key,
    required this.paymentMethod,
    required this.isDarkMode,
    this.onTap,
    this.onSetDefault,
    this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      color: isDarkMode ? Colors.grey[850] : Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: paymentMethod.isDefault
            ? BorderSide(color: theme.colorScheme.primary, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  _buildPaymentMethodIcon(),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                paymentMethod.displayName,
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: isDarkMode ? Colors.white : Colors.black87,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            if (paymentMethod.isDefault)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: theme.colorScheme.primary,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'Default',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                          ],
                        ),
                        if (paymentMethod.lastFourDigits != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            '•••• •••• •••• ${paymentMethod.lastFourDigits}',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: isDarkMode ? Colors.white70 : Colors.black54,
                            ),
                          ),
                        ],
                        if (paymentMethod.type == PaymentMethodType.wallet) ...[
                          const SizedBox(height: 4),
                          Text(
                            'Balance: ﷼${paymentMethod.metadata?['balance']?.toStringAsFixed(2) ?? '0.00'}',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.primary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  PopupMenuButton<String>(
                    icon: Icon(
                      Icons.more_vert,
                      color: isDarkMode ? Colors.white70 : Colors.black54,
                    ),
                    onSelected: (value) {
                      switch (value) {
                        case 'default':
                          onSetDefault?.call();
                          break;
                        case 'remove':
                          onRemove?.call();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      if (!paymentMethod.isDefault)
                        const PopupMenuItem(
                          value: 'default',
                          child: Row(
                            children: [
                              Icon(Icons.star_outline),
                              SizedBox(width: 8),
                              Text('Set as Default'),
                            ],
                          ),
                        ),
                      if (paymentMethod.type != PaymentMethodType.wallet)
                        const PopupMenuItem(
                          value: 'remove',
                          child: Row(
                            children: [
                              Icon(Icons.delete_outline, color: Colors.red),
                              SizedBox(width: 8),
                              Text('Remove', style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                    ],
                  ),
                ],
              ),
              if (paymentMethod.expiryDate != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.schedule,
                      size: 16,
                      color: isDarkMode ? Colors.white70 : Colors.black54,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Expires ${_formatExpiryDate(paymentMethod.expiryDate!)}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: isDarkMode ? Colors.white70 : Colors.black54,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentMethodIcon() {
    IconData iconData;
    Color iconColor;

    switch (paymentMethod.type) {
      case PaymentMethodType.wallet:
        iconData = Icons.account_balance_wallet;
        iconColor = Colors.teal;
        break;
      case PaymentMethodType.googlePay:
        iconData = Icons.android;
        iconColor = Colors.green;
        break;
      case PaymentMethodType.applePay:
        iconData = Icons.apple;
        iconColor = Colors.black;
        break;
      case PaymentMethodType.mada:
        iconData = Icons.credit_card;
        iconColor = Colors.purple;
        break;
      case PaymentMethodType.creditCard:
      case PaymentMethodType.debitCard:
        iconData = Icons.credit_card;
        iconColor = _getCardBrandColor();
        break;
    }

    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: iconColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Icon(
        iconData,
        color: iconColor,
        size: 24,
      ),
    );
  }

  Color _getCardBrandColor() {
    switch (paymentMethod.cardBrand?.toLowerCase()) {
      case 'visa':
        return Colors.blue;
      case 'mastercard':
        return Colors.red;
      case 'amex':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  String _formatExpiryDate(DateTime expiryDate) {
    final month = expiryDate.month.toString().padLeft(2, '0');
    final year = expiryDate.year.toString().substring(2);
    return '$month/$year';
  }
}
