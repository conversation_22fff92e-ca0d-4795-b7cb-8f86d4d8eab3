import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../features/theme/presentation/providers/theme_provider.dart';
import '../../domain/entities/payment_method.dart';
import '../providers/payment_method_provider.dart';

class AddPaymentMethodModal extends ConsumerStatefulWidget {
  const AddPaymentMethodModal({super.key});

  @override
  ConsumerState<AddPaymentMethodModal> createState() => _AddPaymentMethodModalState();
}

class _AddPaymentMethodModalState extends ConsumerState<AddPaymentMethodModal> {
  PaymentMethodType? selectedType;
  bool isLoading = false;

  final List<PaymentMethodOption> paymentOptions = [
    PaymentMethodOption(
      type: PaymentMethodType.googlePay,
      title: 'Google Pay',
      subtitle: 'Pay with your Google account',
      icon: Icons.android,
      color: Colors.green,
    ),
    PaymentMethodOption(
      type: PaymentMethodType.applePay,
      title: 'Apple Pay',
      subtitle: 'Pay with Touch ID or Face ID',
      icon: Icons.apple,
      color: Colors.black,
    ),
    PaymentMethodOption(
      type: PaymentMethodType.mada,
      title: 'Mada Card',
      subtitle: 'Saudi local payment system',
      icon: Icons.credit_card,
      color: Colors.purple,
    ),
    PaymentMethodOption(
      type: PaymentMethodType.creditCard,
      title: 'Credit Card',
      subtitle: 'Visa, Mastercard, Amex',
      icon: Icons.credit_card,
      color: Colors.blue,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final isDarkMode = ref.watch(themeProvider).isDark;
    final theme = Theme.of(context);

    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
      ),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[900] : Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.symmetric(vertical: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: isDarkMode ? Colors.grey[600] : Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Add Payment Method',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: Icon(
                    Icons.close,
                    color: isDarkMode ? Colors.white70 : Colors.black54,
                  ),
                ),
              ],
            ),
          ),
          // Payment options
          Flexible(
            child: ListView.builder(
              shrinkWrap: true,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: paymentOptions.length,
              itemBuilder: (context, index) {
                final option = paymentOptions[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: _buildPaymentOptionCard(option, isDarkMode, theme),
                );
              },
            ),
          ),
          // Add button
          Padding(
            padding: const EdgeInsets.all(16),
            child: SizedBox(
              width: double.infinity,
              child: FilledButton(
                onPressed: selectedType != null && !isLoading
                    ? () => _addPaymentMethod()
                    : null,
                style: FilledButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text(
                        'Add Payment Method',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentOptionCard(
    PaymentMethodOption option,
    bool isDarkMode,
    ThemeData theme,
  ) {
    final isSelected = selectedType == option.type;

    return Card(
      elevation: isSelected ? 4 : 1,
      color: isDarkMode ? Colors.grey[850] : Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isSelected
            ? BorderSide(color: theme.colorScheme.primary, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: () => setState(() => selectedType = option.type),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: option.color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  option.icon,
                  color: option.color,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      option.title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: isDarkMode ? Colors.white : Colors.black87,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      option.subtitle,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: isDarkMode ? Colors.white70 : Colors.black54,
                      ),
                    ),
                  ],
                ),
              ),
              Radio<PaymentMethodType>(
                value: option.type,
                groupValue: selectedType,
                onChanged: (value) => setState(() => selectedType = value),
                activeColor: theme.colorScheme.primary,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _addPaymentMethod() async {
    if (selectedType == null) return;

    setState(() => isLoading = true);

    try {
      // Create a new payment method
      final newPaymentMethod = PaymentMethod(
        id: 'pm_${DateTime.now().millisecondsSinceEpoch}',
        type: selectedType!,
        displayName: _getDisplayName(selectedType!),
        createdAt: DateTime.now(),
        isDefault: false,
      );

      // Add to the list
      final success = await ref.read(paymentMethodsProvider.notifier).addPaymentMethod(newPaymentMethod);

      if (success && mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${newPaymentMethod.displayName} added successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add payment method: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => isLoading = false);
      }
    }
  }

  String _getDisplayName(PaymentMethodType type) {
    switch (type) {
      case PaymentMethodType.googlePay:
        return 'Google Pay';
      case PaymentMethodType.applePay:
        return 'Apple Pay';
      case PaymentMethodType.mada:
        return 'Mada Card';
      case PaymentMethodType.creditCard:
        return 'Credit Card';
      case PaymentMethodType.debitCard:
        return 'Debit Card';
      case PaymentMethodType.wallet:
        return 'Wallet';
    }
  }
}

class PaymentMethodOption {
  final PaymentMethodType type;
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;

  PaymentMethodOption({
    required this.type,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
  });
}
