import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/configs/theme/app_colors.dart';
import '../../../../features/theme/presentation/providers/theme_provider.dart';
import '../../domain/entities/payment_method.dart';
import '../providers/payment_method_provider.dart';
import '../widgets/payment_method_card.dart';
import '../widgets/add_payment_method_modal.dart';

class PaymentMethodsScreen extends ConsumerWidget {
  const PaymentMethodsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDarkMode = ref.watch(themeProvider).isDark;
    final theme = Theme.of(context);
    final paymentMethodsAsync = ref.watch(paymentMethodsProvider);

    return Scaffold(
      backgroundColor: isDarkMode ? Colors.grey[900] : Colors.grey[50],
      appBar: AppBar(
        backgroundColor: isDarkMode ? Colors.grey[850] : Colors.white,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: isDarkMode ? Colors.white : Colors.black87,
          ),
          onPressed: () => context.pop(),
        ),
        title: Text(
          'Payment Methods',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: isDarkMode ? Colors.white : Colors.black87,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: isDarkMode ? Colors.white70 : Colors.black54,
            ),
            onPressed: () => ref.refresh(paymentMethodsProvider),
          ),
        ],
      ),
      body: paymentMethodsAsync.when(
        data: (paymentMethods) => _buildPaymentMethodsList(
          context,
          ref,
          paymentMethods,
          isDarkMode,
          theme,
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => _buildErrorView(
          context,
          ref,
          error.toString(),
          isDarkMode,
          theme,
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showAddPaymentMethodModal(context),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add),
        label: const Text('Add Payment Method'),
      ),
    );
  }

  Widget _buildPaymentMethodsList(
    BuildContext context,
    WidgetRef ref,
    List<PaymentMethod> paymentMethods,
    bool isDarkMode,
    ThemeData theme,
  ) {
    if (paymentMethods.isEmpty) {
      return _buildEmptyState(context, isDarkMode, theme);
    }

    return RefreshIndicator(
      onRefresh: () => ref.refresh(paymentMethodsProvider.future),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: paymentMethods.length,
        itemBuilder: (context, index) {
          final paymentMethod = paymentMethods[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: PaymentMethodCard(
              paymentMethod: paymentMethod,
              isDarkMode: isDarkMode,
              onTap: () => _handlePaymentMethodTap(context, ref, paymentMethod),
              onSetDefault: () => _setDefaultPaymentMethod(ref, paymentMethod.id),
              onRemove: () => _removePaymentMethod(context, ref, paymentMethod),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, bool isDarkMode, ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.payment,
            size: 64,
            color: theme.colorScheme.primary.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No Payment Methods',
            style: theme.textTheme.titleLarge?.copyWith(
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add a payment method to start making payments',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isDarkMode ? Colors.white70 : Colors.black54,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          FilledButton.icon(
            onPressed: () => _showAddPaymentMethodModal(context),
            icon: const Icon(Icons.add),
            label: const Text('Add Payment Method'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(
    BuildContext context,
    WidgetRef ref,
    String error,
    bool isDarkMode,
    ThemeData theme,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: theme.colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to load payment methods',
            style: theme.textTheme.titleLarge?.copyWith(
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.error,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          FilledButton.icon(
            onPressed: () => ref.refresh(paymentMethodsProvider),
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _handlePaymentMethodTap(
    BuildContext context,
    WidgetRef ref,
    PaymentMethod paymentMethod,
  ) {
    // Handle payment method selection or details view
    ref.read(selectedPaymentMethodProvider.notifier).state = paymentMethod;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Selected: ${paymentMethod.displayName}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Future<void> _setDefaultPaymentMethod(WidgetRef ref, String paymentMethodId) async {
    final success = await ref.read(paymentMethodsProvider.notifier).setDefaultPaymentMethod(paymentMethodId);
    // Handle success/failure feedback
  }

  Future<void> _removePaymentMethod(
    BuildContext context,
    WidgetRef ref,
    PaymentMethod paymentMethod,
  ) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove Payment Method'),
        content: Text('Are you sure you want to remove ${paymentMethod.displayName}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Remove'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await ref.read(paymentMethodsProvider.notifier).removePaymentMethod(paymentMethod.id);
      if (success && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Payment method removed')),
        );
      }
    }
  }

  void _showAddPaymentMethodModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const AddPaymentMethodModal(),
    );
  }
}
