import '../../domain/entities/emergency_contact.dart';

class EmergencyContactModel extends EmergencyContact {
  const EmergencyContactModel({
    required super.id,
    required super.name,
    required super.phoneNumber,
    required super.relationship,
    super.isPrimary,
    required super.createdAt,
  });

  factory EmergencyContactModel.fromJson(Map<String, dynamic> json) {
    return EmergencyContactModel(
      id: json['id'] as String,
      name: json['name'] as String,
      phoneNumber: json['phoneNumber'] as String,
      relationship: json['relationship'] as String,
      isPrimary: json['isPrimary'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }

  factory EmergencyContactModel.fromEntity(EmergencyContact entity) {
    return EmergencyContactModel(
      id: entity.id,
      name: entity.name,
      phoneNumber: entity.phoneNumber,
      relationship: entity.relationship,
      isPrimary: entity.isPrimary,
      createdAt: entity.createdAt,
    );
  }

  static List<EmergencyContactModel> getMockContacts() {
    return [
      EmergencyContactModel(
        id: 'ec_001',
        name: '<PERSON>',
        phoneNumber: '+966501234568',
        relationship: 'Spouse',
        isPrimary: true,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      EmergencyContactModel(
        id: 'ec_002',
        name: 'Mohammed Al-Rashid',
        phoneNumber: '+966501234569',
        relationship: 'Father',
        isPrimary: false,
        createdAt: DateTime.now().subtract(const Duration(days: 25)),
      ),
    ];
  }
}
