import 'dart:convert';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import '../../../../core/configs/services/storage/storage_service.dart';
import '../../../../core/error/api_exceptions.dart';
import '../../../../utils/logger.dart';
import '../models/emergency_contact_model.dart';

class EmergencyService {
  final http.Client _client;
  final StorageService _storageService;

  EmergencyService(this._client, this._storageService);

  Future<bool> triggerEmergencyAlert({
    required Position userLocation,
    String? message,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final user = _storageService.getUser();
      if (user == null) throw ApiException('User not authenticated');

      AppLogger.log('🚨 Triggering emergency alert for user: ${user.id}');

      // Get emergency contacts
      final emergencyContacts = await getEmergencyContacts();

      // Prepare emergency data
      final emergencyData = {
        'userId': user.id,
        'userName': '${user.firstName} ${user.lastName}',
        'userPhone': user.phoneNumber,
        'location': {
          'latitude': userLocation.latitude,
          'longitude': userLocation.longitude,
          'accuracy': userLocation.accuracy,
          'timestamp': DateTime.now().toIso8601String(),
        },
        'message': message ?? 'Emergency alert triggered from e-scooter app',
        'emergencyContacts': emergencyContacts.map((contact) => contact.toJson()).toList(),
        'additionalData': additionalData,
        'timestamp': DateTime.now().toIso8601String(),
      };

      // Send to emergency services API (mock for now)
      AppLogger.log('📞 Notifying emergency services...');
      await _notifyEmergencyServices(emergencyData);

      // Send to emergency contacts
      AppLogger.log('📱 Notifying emergency contacts...');
      await _notifyEmergencyContacts(emergencyContacts, emergencyData);

      // Store emergency alert locally
      await _storeEmergencyAlert(emergencyData);

      AppLogger.log('✅ Emergency alert sent successfully');
      return true;
    } catch (e) {
      AppLogger.error('❌ Failed to trigger emergency alert', error: e);
      return false;
    }
  }

  Future<void> _notifyEmergencyServices(Map<String, dynamic> emergencyData) async {
    try {
      // In a real implementation, this would call actual emergency services API
      // For now, we'll simulate the call
      await Future.delayed(const Duration(seconds: 1));
      
      AppLogger.log('🚑 Emergency services notified');
      AppLogger.log('📍 Location: ${emergencyData['location']['latitude']}, ${emergencyData['location']['longitude']}');
    } catch (e) {
      AppLogger.error('Failed to notify emergency services', error: e);
      rethrow;
    }
  }

  Future<void> _notifyEmergencyContacts(
    List<EmergencyContactModel> contacts,
    Map<String, dynamic> emergencyData,
  ) async {
    try {
      for (final contact in contacts) {
        // In a real implementation, this would send SMS or call the contact
        // For now, we'll simulate the notification
        await Future.delayed(const Duration(milliseconds: 500));
        
        AppLogger.log('📞 Notified ${contact.name} at ${contact.phoneNumber}');
      }
    } catch (e) {
      AppLogger.error('Failed to notify emergency contacts', error: e);
      rethrow;
    }
  }

  Future<void> _storeEmergencyAlert(Map<String, dynamic> emergencyData) async {
    try {
      final alerts = _storageService.getStringList('emergency_alerts') ?? [];
      alerts.add(jsonEncode(emergencyData));
      
      // Keep only last 10 alerts
      if (alerts.length > 10) {
        alerts.removeRange(0, alerts.length - 10);
      }
      
      await _storageService.setStringList('emergency_alerts', alerts);
    } catch (e) {
      AppLogger.error('Failed to store emergency alert', error: e);
    }
  }

  Future<List<EmergencyContactModel>> getEmergencyContacts() async {
    try {
      final contactsJson = _storageService.getStringList('emergency_contacts') ?? [];
      return contactsJson
          .map((json) => EmergencyContactModel.fromJson(jsonDecode(json)))
          .toList();
    } catch (e) {
      AppLogger.error('Failed to get emergency contacts', error: e);
      return [];
    }
  }

  Future<bool> addEmergencyContact(EmergencyContactModel contact) async {
    try {
      final contacts = await getEmergencyContacts();
      
      // Check if contact already exists
      if (contacts.any((c) => c.phoneNumber == contact.phoneNumber)) {
        throw ApiException('Contact with this phone number already exists');
      }
      
      contacts.add(contact);
      
      final contactsJson = contacts.map((c) => jsonEncode(c.toJson())).toList();
      await _storageService.setStringList('emergency_contacts', contactsJson);
      
      AppLogger.log('✅ Emergency contact added: ${contact.name}');
      return true;
    } catch (e) {
      AppLogger.error('Failed to add emergency contact', error: e);
      return false;
    }
  }

  Future<bool> removeEmergencyContact(String contactId) async {
    try {
      final contacts = await getEmergencyContacts();
      contacts.removeWhere((c) => c.id == contactId);
      
      final contactsJson = contacts.map((c) => jsonEncode(c.toJson())).toList();
      await _storageService.setStringList('emergency_contacts', contactsJson);
      
      AppLogger.log('✅ Emergency contact removed');
      return true;
    } catch (e) {
      AppLogger.error('Failed to remove emergency contact', error: e);
      return false;
    }
  }

  Future<List<Map<String, dynamic>>> getEmergencyAlerts() async {
    try {
      final alertsJson = _storageService.getStringList('emergency_alerts') ?? [];
      return alertsJson.map((json) => jsonDecode(json) as Map<String, dynamic>).toList();
    } catch (e) {
      AppLogger.error('Failed to get emergency alerts', error: e);
      return [];
    }
  }
}
