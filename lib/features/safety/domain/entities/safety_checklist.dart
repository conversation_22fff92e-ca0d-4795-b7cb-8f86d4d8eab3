class SafetyChecklistItem {
  final String id;
  final String title;
  final String description;
  final bool isRequired;
  final int order;

  const SafetyChecklistItem({
    required this.id,
    required this.title,
    required this.description,
    this.isRequired = true,
    required this.order,
  });

  factory SafetyChecklistItem.fromJson(Map<String, dynamic> json) {
    return SafetyChecklistItem(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      isRequired: json['isRequired'] as bool? ?? true,
      order: json['order'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'isRequired': isRequired,
      'order': order,
    };
  }
}

class SafetyChecklist {
  final List<SafetyChecklistItem> items;
  final Map<String, bool> completedItems;

  const SafetyChecklist({
    required this.items,
    this.completedItems = const {},
  });

  bool get isCompleted {
    final requiredItems = items.where((item) => item.isRequired);
    return requiredItems.every((item) => completedItems[item.id] == true);
  }

  double get completionPercentage {
    if (items.isEmpty) return 1.0;
    final completedCount = items.where((item) => completedItems[item.id] == true).length;
    return completedCount / items.length;
  }

  SafetyChecklist copyWith({
    List<SafetyChecklistItem>? items,
    Map<String, bool>? completedItems,
  }) {
    return SafetyChecklist(
      items: items ?? this.items,
      completedItems: completedItems ?? this.completedItems,
    );
  }

  SafetyChecklist toggleItem(String itemId) {
    final newCompletedItems = Map<String, bool>.from(completedItems);
    newCompletedItems[itemId] = !(completedItems[itemId] ?? false);
    return copyWith(completedItems: newCompletedItems);
  }

  static List<SafetyChecklistItem> getDefaultItems() {
    return [
      const SafetyChecklistItem(
        id: 'helmet_check',
        title: 'Helmet Safety',
        description: 'Ensure you are wearing a proper helmet for your safety',
        isRequired: true,
        order: 1,
      ),
      const SafetyChecklistItem(
        id: 'scooter_inspection',
        title: 'Scooter Inspection',
        description: 'Check brakes, lights, and overall scooter condition',
        isRequired: true,
        order: 2,
      ),
      const SafetyChecklistItem(
        id: 'traffic_rules',
        title: 'Traffic Rules Awareness',
        description: 'I understand and will follow local traffic rules',
        isRequired: true,
        order: 3,
      ),
      const SafetyChecklistItem(
        id: 'emergency_contacts',
        title: 'Emergency Contacts',
        description: 'Emergency contacts are updated and accessible',
        isRequired: false,
        order: 4,
      ),
      const SafetyChecklistItem(
        id: 'weather_conditions',
        title: 'Weather Check',
        description: 'Weather conditions are suitable for riding',
        isRequired: true,
        order: 5,
      ),
    ];
  }
}
