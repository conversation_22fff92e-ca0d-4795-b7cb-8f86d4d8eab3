import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geolocator/geolocator.dart';
import '../../../../utils/logger.dart';
import '../../data/models/emergency_contact_model.dart';
import '../../data/services/emergency_service.dart';
import '../../domain/entities/emergency_contact.dart';
import '../../../../core/di/service_locator/service_locator.dart';

// Provider for emergency contacts
final emergencyContactsProvider = AsyncNotifierProvider<EmergencyContactsNotifier, List<EmergencyContact>>(() {
  return EmergencyContactsNotifier();
});

// Provider for emergency alert state
final emergencyAlertProvider = StateProvider<bool>((ref) => false);

// Provider for safety checklist completion
final safetyChecklistProvider = StateProvider<Map<String, bool>>((ref) => {});

class EmergencyContactsNotifier extends AsyncNotifier<List<EmergencyContact>> {
  late EmergencyService _emergencyService;

  @override
  Future<List<EmergencyContact>> build() async {
    try {
      // For now, we'll create a mock service since we don't have DI setup
      _emergencyService = EmergencyService(
        getIt.get(), // http client
        getIt.get(), // storage service
      );
    } catch (e) {
      AppLogger.error('Failed to initialize emergency service', error: e);
      // Return mock data for now
      return EmergencyContactModel.getMockContacts();
    }
    
    return await _fetchEmergencyContacts();
  }

  Future<List<EmergencyContact>> _fetchEmergencyContacts() async {
    try {
      AppLogger.log('EmergencyContactsNotifier: Fetching emergency contacts');
      
      // For now, return mock data since we don't have backend
      final contacts = EmergencyContactModel.getMockContacts();
      
      AppLogger.log('EmergencyContactsNotifier: Found ${contacts.length} emergency contacts');
      return contacts;
    } catch (e) {
      AppLogger.error('EmergencyContactsNotifier: Error fetching emergency contacts', error: e);
      rethrow;
    }
  }

  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _fetchEmergencyContacts());
  }

  Future<bool> addEmergencyContact(EmergencyContact contact) async {
    try {
      AppLogger.log('EmergencyContactsNotifier: Adding emergency contact: ${contact.name}');
      
      // For now, just add to the current list since we don't have backend
      final currentContacts = state.value ?? [];
      final updatedContacts = [...currentContacts, contact];
      
      state = AsyncValue.data(updatedContacts);
      
      AppLogger.log('EmergencyContactsNotifier: Emergency contact added successfully');
      return true;
    } catch (e) {
      AppLogger.error('EmergencyContactsNotifier: Failed to add emergency contact', error: e);
      return false;
    }
  }

  Future<bool> removeEmergencyContact(String contactId) async {
    try {
      AppLogger.log('EmergencyContactsNotifier: Removing emergency contact: $contactId');
      
      final currentContacts = state.value ?? [];
      final updatedContacts = currentContacts.where((contact) => contact.id != contactId).toList();
      
      state = AsyncValue.data(updatedContacts);
      
      AppLogger.log('EmergencyContactsNotifier: Emergency contact removed successfully');
      return true;
    } catch (e) {
      AppLogger.error('EmergencyContactsNotifier: Failed to remove emergency contact', error: e);
      return false;
    }
  }

  Future<bool> setPrimaryContact(String contactId) async {
    try {
      AppLogger.log('EmergencyContactsNotifier: Setting primary contact: $contactId');
      
      final currentContacts = state.value ?? [];
      final updatedContacts = currentContacts.map((contact) {
        return contact.copyWith(isPrimary: contact.id == contactId);
      }).toList();
      
      state = AsyncValue.data(updatedContacts);
      
      AppLogger.log('EmergencyContactsNotifier: Primary contact set successfully');
      return true;
    } catch (e) {
      AppLogger.error('EmergencyContactsNotifier: Failed to set primary contact', error: e);
      return false;
    }
  }
}

// Provider for triggering emergency alerts
final triggerEmergencyAlertProvider = Provider<Future<bool> Function({String? message})>((ref) {
  return ({String? message}) async {
    try {
      AppLogger.log('🚨 Triggering emergency alert');
      
      // Set emergency state
      ref.read(emergencyAlertProvider.notifier).state = true;
      
      // Get current location
      Position? userLocation;
      try {
        userLocation = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high,
        );
      } catch (e) {
        AppLogger.error('Failed to get location for emergency alert', error: e);
        // Continue without location
      }
      
      // Simulate emergency alert processing
      await Future.delayed(const Duration(seconds: 2));
      
      // For now, just simulate success
      AppLogger.log('✅ Emergency alert sent successfully');
      
      return true;
    } catch (e) {
      AppLogger.error('❌ Failed to trigger emergency alert', error: e);
      return false;
    } finally {
      // Reset emergency state
      ref.read(emergencyAlertProvider.notifier).state = false;
    }
  };
});

// Helper provider to get primary emergency contact
final primaryEmergencyContactProvider = Provider<EmergencyContact?>((ref) {
  final emergencyContacts = ref.watch(emergencyContactsProvider);
  
  return emergencyContacts.when(
    data: (contacts) => contacts.where((contact) => contact.isPrimary).firstOrNull,
    loading: () => null,
    error: (_, __) => null,
  );
});

// Helper provider to check if emergency contacts are set up
final hasEmergencyContactsProvider = Provider<bool>((ref) {
  final emergencyContacts = ref.watch(emergencyContactsProvider);
  
  return emergencyContacts.when(
    data: (contacts) => contacts.isNotEmpty,
    loading: () => false,
    error: (_, __) => false,
  );
});
