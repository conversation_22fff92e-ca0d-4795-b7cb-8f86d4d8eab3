import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/emergency_provider.dart';

class EmergencyButton extends ConsumerStatefulWidget {
  final VoidCallback? onPressed;
  final double size;
  final bool showLabel;

  const EmergencyButton({
    super.key,
    this.onPressed,
    this.size = 64,
    this.showLabel = true,
  });

  @override
  ConsumerState<EmergencyButton> createState() => _EmergencyButtonState();
}

class _EmergencyButtonState extends ConsumerState<EmergencyButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;
  bool _isPressed = false;
  int _pressCount = 0;
  static const int _requiredPresses = 3;
  static const Duration _pressTimeout = Duration(seconds: 2);

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handlePress() async {
    if (_isPressed) return;

    setState(() {
      _pressCount++;
    });

    // Haptic feedback
    HapticFeedback.heavyImpact();

    if (_pressCount >= _requiredPresses) {
      await _triggerEmergencyAlert();
    } else {
      // Show progress and reset after timeout
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Press ${_requiredPresses - _pressCount} more times to trigger emergency alert',
          ),
          duration: _pressTimeout,
          backgroundColor: Colors.orange,
        ),
      );

      // Reset count after timeout
      Future.delayed(_pressTimeout, () {
        if (mounted) {
          setState(() {
            _pressCount = 0;
          });
        }
      });
    }
  }

  Future<void> _triggerEmergencyAlert() async {
    setState(() {
      _isPressed = true;
      _pressCount = 0;
    });

    try {
      // Show confirmation dialog
      final confirmed = await _showEmergencyConfirmation();
      
      if (confirmed == true) {
        // Trigger emergency alert
        final triggerAlert = ref.read(triggerEmergencyAlertProvider);
        final success = await triggerAlert(message: 'Emergency alert triggered from e-scooter app');

        if (mounted) {
          if (success) {
            _showSuccessDialog();
          } else {
            _showErrorDialog();
          }
        }
      }
    } finally {
      if (mounted) {
        setState(() {
          _isPressed = false;
        });
      }
    }

    widget.onPressed?.call();
  }

  Future<bool?> _showEmergencyConfirmation() async {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.red, size: 32),
            SizedBox(width: 12),
            Text('Emergency Alert'),
          ],
        ),
        content: const Text(
          'This will send an emergency alert to your emergency contacts and local authorities with your current location. Do you want to continue?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: FilledButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('Send Alert'),
          ),
        ],
      ),
    );
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 32),
            SizedBox(width: 12),
            Text('Alert Sent'),
          ],
        ),
        content: const Text(
          'Emergency alert has been sent successfully. Help is on the way.',
        ),
        actions: [
          FilledButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.error, color: Colors.red, size: 32),
            SizedBox(width: 12),
            Text('Alert Failed'),
          ],
        ),
        content: const Text(
          'Failed to send emergency alert. Please try again or contact emergency services directly.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              Navigator.of(context).pop();
              _triggerEmergencyAlert();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isEmergencyActive = ref.watch(emergencyAlertProvider);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _isPressed ? 0.9 : _pulseAnimation.value,
              child: Container(
                width: widget.size,
                height: widget.size,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      Colors.red.shade400,
                      Colors.red.shade700,
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.red.withOpacity(0.4),
                      blurRadius: 20,
                      spreadRadius: _pulseAnimation.value * 5,
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: _isPressed || isEmergencyActive ? null : _handlePress,
                    borderRadius: BorderRadius.circular(widget.size / 2),
                    child: Container(
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: _isPressed || isEmergencyActive
                            ? const SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 3,
                                ),
                              )
                            : Icon(
                                Icons.emergency,
                                color: Colors.white,
                                size: widget.size * 0.4,
                              ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
        if (widget.showLabel) ...[
          const SizedBox(height: 8),
          Text(
            _pressCount > 0 
                ? 'Press ${_requiredPresses - _pressCount} more times'
                : 'Emergency',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.red,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}
