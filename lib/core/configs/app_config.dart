/// Application Configuration
/// This file contains configuration settings for the app
class AppConfig {
  // Set this to true to use mock authentication for testing
  // Set to false to use real backend API
  static const bool USE_MOCK_AUTH = true;
  
  // Demo user credentials for testing
  static const String DEMO_PHONE_1 = "+966501234567";
  static const String DEMO_OTP_1 = "123456";

  static const String DEMO_PHONE_2 = "+966551234567";
  static const String DEMO_OTP_2 = "654321";

  static const String DEMO_PHONE_3 = "+1234567890";
  static const String DEMO_OTP_3 = "111111";

  static const String DEMO_PHONE_4 = "+971501234567";
  static const String DEMO_OTP_4 = "222222";

  static const String DEMO_PHONE_5 = "+1111111111";
  static const String DEMO_OTP_5 = "999999";

  // Instructions for testing
  static const String TESTING_INSTRUCTIONS = '''
🔧 DEMO TESTING INSTRUCTIONS:

📱 Available Demo Users:
1. 🇸🇦 Phone: +966501234567, OTP: 123456 (<PERSON>)
2. 🇸🇦 Phone: +966551234567, OTP: 654321 (Fatima Al-Zahra)
3. 🇺🇸 Phone: +1234567890, OTP: 111111 (John Doe)
4. 🇦🇪 Phone: +971501234567, OTP: 222222 (Omar Hassan)
5. 🧪 Phone: +1111111111, OTP: 999999 (Test User)

🚀 How to Test:
1. Select country code and enter phone number (without country code)
2. Tap "Continue" to send OTP
3. Enter the corresponding OTP code on next screen
4. You'll be logged in with demo data

⚙️ To switch to real backend:
- Set USE_MOCK_AUTH = false in lib/core/configs/app_config.dart
- Ensure your backend services are running

📝 Note: The app uses Saudi Arabia (+966) as default country code
''';
  
  // App version and build info
  static const String APP_VERSION = "1.0.0";
  static const String BUILD_NUMBER = "1";
  
  // Feature flags
  static const bool ENABLE_LOGGING = true;
  static const bool ENABLE_CRASH_REPORTING = false;
  static const bool ENABLE_ANALYTICS = false;
}
