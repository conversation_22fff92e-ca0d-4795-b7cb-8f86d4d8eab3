import 'package:escooter/common/router/app_router.dart';
import 'package:escooter/core/configs/services/storage/storage_service.dart';
import 'package:escooter/core/configs/theme/app_theme.dart';
import 'package:escooter/core/di/service_locator/service_locator.dart';
import 'package:escooter/core/locale/providers/data/locale_data.dart';
import 'package:escooter/features/home/<USER>/model/user_model.dart';
import 'package:escooter/features/theme/presentation/providers/theme_provider.dart';
import 'package:escooter/l10n/app_localizations.dart';
import 'package:escooter/utils/logger.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  try {
    AppLogger.log('====================================');
    await configureDependencies();
    final storageService = getIt<StorageService>();
    await storageService.initHive();

    // BYPASS AUTH: Create a mock user and set as authenticated
    await _createMockUserAndAuthenticate(storageService);

    await AppRouter.authNotifier.initializeAuth();

    final user = storageService.getUser();
    if (user != null) {
      AppLogger.log('Mock User Created - Token: ${user.token}');
      AppLogger.log('Mock User Name: ${user.firstName} ${user.lastName}');
    } else {
      AppLogger.log('No user token found');
    }
    AppLogger.log('Dependencies configured successfully');
    AppLogger.log('====================================');
  } catch (e) {
    AppLogger.error('Failed to configure dependencies: $e');
  }
  runApp(ProviderScope(child: const MyApp()));
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final locale = ref.watch(localeProvider);
    final themeState = ref.watch(themeProvider);

    return MaterialApp.router(
      debugShowCheckedModeBanner: false,
      routerConfig: AppRouter.router,
      locale: locale,
      supportedLocales: const [
        Locale('en'),
        Locale('ar'),
      ],
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      title: 'Barq Scoot',
      theme: themeState.isDark ? AppTheme.darkTheme : AppTheme.lightTheme,
      builder: (context, child) {
        return Directionality(
          textDirection: locale.languageCode == 'ar'
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: child!,
        );
      },
    );
  }
}

/// Helper function to create a mock user and set authentication state
Future<void> _createMockUserAndAuthenticate(StorageService storageService) async {
  try {
    // Create a mock user
    final mockUser = UserModel(
      id: "mock_user_966501234567",
      token: "mock_jwt_token_12345",
      phoneNumber: "+966501234567",
      firstName: "Ahmed",
      lastName: "Al-Rashid",
      email: "<EMAIL>",
      isVerified: true,
      dateOfBirth: DateTime(1990, 1, 1),
      gender: "male",
      walletBalance: 25.50,
      createdAt: DateTime.now(),
    );

    // Save the mock user
    await storageService.saveUser(mockUser);

    // Set authentication state to true
    await storageService.setBool('isLoggedIn', true);

    AppLogger.log('🔧 Mock user created and authenticated successfully');
    AppLogger.log('📱 User: ${mockUser.firstName} ${mockUser.lastName}');
    AppLogger.log('📞 Phone: ${mockUser.phoneNumber}');
    AppLogger.log('💰 Wallet Balance: \$${mockUser.walletBalance}');
  } catch (e) {
    AppLogger.error('Failed to create mock user: $e');
  }
}
