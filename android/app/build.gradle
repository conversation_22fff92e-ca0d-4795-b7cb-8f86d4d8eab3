plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace = "com.example.escooter"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        applicationId = "com.example.escooter"
        // Setting explicit minSdk for Mapbox compatibility
        minSdk = 21  // Mapbox requires minimum SDK 21
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName

        // Mapbox configuration
        // manifestPlaceholders = [
        //     MAPBOX_ACCESS_TOKEN: "pk.eyJ1IjoiZGF2aW5kZXIxNDM2IiwiYSI6ImNtNmh5bWpxaTAyeHgycXNoa3M5Mmg1N2gifQ.z2lT6pQZZntilXV90lz4aw"
        // ]

        // Configure NDK for different architectures
        // ndk {
        //     abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86_64'
        // }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.debug
        }
    }
}

// dependencies {
//     // Required Mapbox dependencies
//     implementation "com.mapbox.mapboxsdk:mapbox-android-core:3.1.0"
//     implementation "com.mapbox.mapboxsdk:mapbox-android-telemetry:8.1.0"
// }

flutter {
    source = "../.."
}